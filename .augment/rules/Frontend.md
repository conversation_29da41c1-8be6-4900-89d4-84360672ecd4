---
type: "agent_requested"
description: "Example description"
---
When working on the frontend, please ensure that you are following the best practices for performance and accessibility. This includes using semantic HTML, optimizing images, and minimizing HTTP requests. Additionally, please ensure that all code is properly formatted and linted before committing.
Also, please ensure that all code is properly formatted and linted before committing.
Please use the @kickoffscore/frontend documentation as a reference for best practices.
When working on frontend please make sure to work in performance of the website and make it as fast as possible.
Also make sure website does not use too much memory and CPU.
If we create a button we will use same style for all buttons.
If we create a card we will use same style for all cards.
If we create a form we will use same style for all forms.
If we create a table we will use same style for all tables.
If we create a modal we will use same style for all modals.
If we create a dropdown we will use same style for all dropdowns.
If we create a tooltip we will use same style for all tooltips.
Please use this colour pallet for the website:
Monochromatic Minimalism
A simple monochromatic palette can give your dark mode website a clean, elegant, and professional look. By using varying shades of gray and black, you maintain a minimalist aesthetic while still providing depth and contrast. The key here is balance—light grays for text and secondary elements, dark grays for backgrounds, and black for emphasis.
Palette:
Background: #121212 (charcoal black)
Primary Text: #E0E0E0 (light gray)
Secondary Text: #B0B0B0 (medium gray)
Borders/Dividers: #444444 (dark gray)
Accent: #888888 (soft gray)