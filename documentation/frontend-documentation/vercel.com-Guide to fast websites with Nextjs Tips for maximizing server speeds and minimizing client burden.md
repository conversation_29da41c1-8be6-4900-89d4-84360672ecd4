**Guide** **to** **fast** **websites** **with** **Next.js:** **Tips**
**for** **maximizing** **server** **speeds** **and** **minimizing**
**client** **burden**

> <img src="./cf4r1jeq.png"
> style="width:0.16153in;height:0.16153in" />[**vercel.com**/blog/guide-to-fast-websites-with-next-js-tips-for-maximizing-server-speeds](https://vercel.com/blog/guide-to-fast-websites-with-next-js-tips-for-maximizing-server-speeds)
>
> <PERSON><PERSON> Co-founder November 29, 2023

[<u>Blo</u>g](https://vercel.com/blog) /
[**<u>Community</u>**](https://vercel.com/blog/category/community)

8 min read

Tin<PERSON>of Co-founder <PERSON><PERSON> gives insights into how his team
measures website speed with best practices to make faster websites.

[<u><PERSON>lo<PERSON></u> is an agency obsessed with delivering fast websites such
as jewelry brand <u>Jennifer</u>](https://jenniferfisher.com/)
[<u>Fisher</u>, which went from a Shopify theme to a
modern](https://jenniferfisher.com/)
[<u>Next.</u>j<u>s</u>](https://nextjs.org/) [website that instantly
loads](https://jenniferfisher.com/) with 80% less JavaScript.

When evaluating the speed of a website, they look at key metrics in a
typical user journey:

> 1\. **Server** **response** **time:** How long it takes for the user
> to get any feedback once landed on the page.
>
> 2\. **Page** **render** **time:** How long it takes for a page to
> become fully visible and interactive.
>
> 3\. **User** **interaction** **time:** How long it takes the user to
> make key interactions on the page such as navigating between pages or
> adding an item to the cart.

This article covers tips to measure and make each part of the user
journey as fast as it gets.

**<u>The basics of site speed: Measuring data correctly</u>**

The key to making your site fast and keeping it fast—for any user on any
device—is data. Speed is more than just a way to gauge user experience,
it's crucial for getting the top spot on any search platform and winning
organic page views.

To ensure they're measuring the right data correctly, tools like
[<u>Google PageSpeed Insights</u>](https://pagespeed.web.dev/) and
[<u>Vercel Speed
Insi</u>g<u>hts</u>](https://vercel.com/docs/speed-insights) are able to
provide objective metrics. These tools can be used to diagnose page
performance issues, providing insights into aspects like loading times,
interactivity, and visual stability.

It's also equally important to test the user journey under various
network conditions.

Combining objective tools with a hands-on approach provides a
comprehensive view of a website experience, ensuring it’s optimised for
all users.

> <img src="./kwoywmtm.png"
> style="width:1.82734in;height:0.16071in" />[**Vercel** **Speed**
> **Insights**](https://vercel.com/docs/speed-insights)

[<u>Learn how to get a detailed view of your website's performance
metrics to help facilitate</u>](https://vercel.com/docs/speed-insights)
[<u>informed decisions on
optimization.</u>](https://vercel.com/docs/speed-insights)

> 1/18

[<u>Get
Started</u>](https://vercel.com/docs/speed-insights)<img src="./i42lbqzw.png"
style="width:0.48459in;height:0.48459in" />

**<u>How to speed up server response: Make use of Next.js’
rendering</u>** **<u>toolbox</u>**

Once key performance metrics are evaluated, the team is able to pinpoint
where and how to make improvements in things like server response.

**<u>When possible: Pre-render the entire pa</u>g<u>e</u>**

Pre-rendering the page at build-time ensures it is served from a CDN
instead of your origin server, resulting in the fastest server response
possible. This is done automatically by Next.js if you don’t use the
edge runtime and the page doesn’t rely on cookies, headers, or search
parameters.

[<u>Will
Thomson</u>](https://x.com/willthomson__/status/1704137045159911477)

[@<u>willthomson\_\_</u>](https://x.com/willthomson__/status/1704137045159911477)
[·<u>Follow</u>](https://x.com/intent/follow?screen_name=willthomson__)

Caching turns god-mode on for your website. Look at the speed of
[@<u>Tinloof</u>](https://x.com/Tinloof)'s website. You type, hit enter,
site appears. No loading spinners. No waiting. Optimisation is why I
choose code over no-code. P.S They have epic developer resources on
their site.

> <img src="./3haf35o3.png"
> style="width:6.86502in;height:4.29064in" />0:00

[<u>Watch on
X</u>](https://x.com/willthomson__/status/1704137045159911477)

> 2/18

[<u>25</u>](https://x.com/intent/like?tweet_id=1704137045159911477)<img src="./nvqpmh0l.png"
style="width:0.48459in;height:0.48459in" />

[<u>Reply</u>](https://x.com/intent/tweet?in_reply_to=1704137045159911477)

[<u>Read 4
replies</u>](https://x.com/willthomson__/status/1704137045159911477)

**<u>Else: Partial Prerender</u>**

Pre-rendering an entire page might not be always possible.

With [<u>Partial
Prerenderin</u>g](https://vercel.com/blog/partial-prerendering-with-next-js-creating-a-new-default-rendering-model)
(PPR) in Next.js, it's possible to pre-render a shell of the page that
is served from a CDN while streaming the dynamic bits at the same time.

Partial Prerendering is currently [<u>an experimental feature</u>
t](https://twitter.com/leeerob/status/1723039087391543654)hat allows you
to render a route with a static loading shell, while keeping some parts
dynamic. In other words, you can isolate the dynamic parts of a page
instead of a whole route.

**<u>Final resort: Render a loading shell while waiting for the final
response</u>**

When a page is rendered at request time, it’s better to immediately show
the user a UI hint that indicates a page is loading, rather than
unresponsive links.

It’s best to make the loading UI resemble as much as possible the final
one.

In Next.js, there are two places where you can render the loading UI:

> In a
> [<u>loadin</u>g<u>.tsx</u>](https://nextjs.org/docs/app/building-your-application/routing/loading-ui-and-streaming)
> file inside the page route folder.

[<u>Alex
Sidorenko</u>](https://x.com/asidorenko_/status/1696549318495178887)

[<u>@asidorenko\_</u>](https://x.com/asidorenko_/status/1696549318495178887)
[·<u>Follow</u>](https://x.com/intent/follow?screen_name=asidorenko_)

"I have one server component that fetches a lot of data and makes the
entire page slow to load in Next.js 13" Wrap it with Suspense

> 3/18
>
> <img src="./u12ddwup.png"
> style="width:6.86502in;height:3.86662in" />0:00

[<u>Watch on
X</u>](https://x.com/asidorenko_/status/1696549318495178887)

[<u>945</u>](https://x.com/intent/like?tweet_id=1696549318495178887)

[<u>Reply</u>](https://x.com/intent/tweet?in_reply_to=1696549318495178887)

[<u>Read 18
replies</u>](https://x.com/asidorenko_/status/1696549318495178887)

> Inside the fallback prop of the [<u>Suspense
> boundary</u>](https://nextjs.org/docs/app/building-your-application/routing/loading-ui-and-streaming#streaming-with-suspense)
> that wraps async component making requests inside the page.

**<u>Cache fetch requests for fast server responses when using loading
spinners</u>**

Loading shells are not an excuse for slow server responses. Most server
responses can be cached instead of making the user wait for them on
every page visit.

Although this is the default behaviour of fetch requests in Next.js, you
can still control the freshness of this data:

> By revalidating the server response every x number seconds.

app/page.tsx

> 4/18

exportdefaultfunctionHome(){<img src="./lvqcclbc.png"
style="width:0.15451in;height:0.13715in" />

// The CMS data is guaranteed to be fresh every 2 minutes

const cmsData =awaitfetch(\`https://...\`,{ next:{ revalidate:120}});

return\<h1\>{cmsData.title}\</h1\>

}

> Or by revalidating the server response when a certain event happens.
> Here’s an example where a CMS response is revalidated whenever a new
> CMS page gets published.

app/page.tsx

exportdefaultfunctionHome(){

// The CMS data is cached until the tag is revalidated

const cmsData =awaitfetch(\`https://...\`,{ next:{
tags:\['landing-page'\]);

return\<h1\>{cmsData.title}\</h1\>

}

app/api/revalidate/route.ts

> 5/18

import{ revalidateTag }from'next/cache';

import{NextRequest,NextResponse}from'next/server';

exportasyncfunctionPOST(req: NextRequest):Promise\<NextResponse\>{

const secret = req.nextUrl.searchParams.get('secret');

const tag = req.nextUrl.searchParams.get('landing-page');

if(!tag \|\|!isValid(secret)){

returnNextResponse.json({ status:400});

}

returnrevalidate(tag);

}

The [<u>Next.js guide on cachin</u>g <u>and
revalidation</u>](https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating)
and the [<u>App Router explainer
video</u>](https://www.youtube.com/watch?v=gSSsZReIFRk&t=577s&ab_channel=Vercel)
are perfect to help understand these concepts.

**<u>How to speed up the pa</u>g<u>e render: Minimize client
burden</u>**

**Short** **answer:** Make the browser do the least amount of work to
render the page.

Once the browser receives a response from the server, it still has to
paint the entire page and make it ready for user interactions (e.g.
button clicks).

While parsing the HTML and rendering, the browser is also downloading
resources such as CSS, JavaScript, font, or image files.

The following tips help make page render fast by making the browser do
as little work as possible.

**<u>Reduce the JavaScript bundle size and minimize the impact of
hydration</u>**

The JavaScript shipped with React websites usually consists of React,
Next.js, the application code including the JSX of every single React
component, and third-party dependencies.

Once the page HTML is rendered and the JavaScript is downloaded, React
goes through a process called
“[<u>hydration</u>”](https://chat.openai.com/share/4bf8f74b-1681-40c6-aae3-dc1ae00013db)
where it attaches event listeners and state to the components of the
page.

> 6/18

Just by using [<u>React Server
Components</u>](https://nextjs.org/docs/app/building-your-application/rendering/server-components)
you already get a speed bump because:<img src="./z2zph3ih.png"
style="width:0.48459in;height:0.48459in" /><img src="./rfunipp0.png"
style="width:6.55206in;height:1.72635in" />

> 1\. Their JavaScript (including application code, JSX, and third-party
> dependencies) is not shipped to the browser.
>
> 2\. React skips their hydration.

[<u>Guillermo
Rauch</u>](https://x.com/rauchg/status/1696944713708757065)

[<u>@rauch</u>g](https://x.com/rauchg/status/1696944713708757065)
[·<u>Follow</u>](https://x.com/intent/follow?screen_name=rauchg)

We moved our Web Analytics dashboard pages to
[<u>@nextjs</u>](https://x.com/nextjs) App Router and shaved off 800ms
of LCP. What a time to be alive.

[<u>6:55 PM · Aug 30,
2023</u>](https://x.com/rauchg/status/1696944713708757065)

[<u>596</u>](https://x.com/intent/like?tweet_id=1696944713708757065)

[<u>Reply</u>](https://x.com/intent/tweet?in_reply_to=1696944713708757065)

[<u>Read 23
replies</u>](https://x.com/rauchg/status/1696944713708757065)

When a component requires interactivity (e.g. state, event listeners), a
use client directive can be used to convert it to a client component
which in addition of being rendered in the server, also has its
JavaScript shipped to the browser and is hydrated by React.

**<u>Reduce the impact of client components on pa</u>g<u>e speed</u>**

**<u>Only use client components when necessary</u>**

URLs can be used to store a component state without having to make it a
client component that relies on React’s state.

It requires less code to manage the state, turns state buttons to links
that work even without JavaScript, and makes it possible to persist the
state on page refresh or when sharing the URL.

> 7/18

<img src="./3ppdrdw3.png"
style="width:0.48459in;height:0.48459in" /><img src="./a41lcwdb.png"
style="width:0.48459in;height:0.48459in" />

[<u>Lee Robinson</u>](https://x.com/leerob/status/1688639192723521536)

[<u>@leerob</u>](https://x.com/leerob/status/1688639192723521536)
[·<u>Follow</u>](https://x.com/intent/follow?screen_name=leerob)

[<u>Replying to
@leerob</u>](https://x.com/leerob/status/1688639191276507136)

From \`useState\` to URL state. Rather than using client-side React
state, we can instead lift state up to the URL for color, size, and even
the selected image. With the added bonus you can now "deep link" to
specific variations. Yay, using the web platform!

> <img src="./y5adk3ci.png"
> style="width:6.86502in;height:4.25026in" />0:00

[<u>Watch on X</u>](https://x.com/leerob/status/1688639192723521536)

[<u>8:52 PM · Aug 7,
2023</u>](https://x.com/leerob/status/1688639192723521536)

[<u>98</u>](https://x.com/intent/like?tweet_id=1688639192723521536)

[<u>Reply</u>](https://x.com/intent/tweet?in_reply_to=1688639192723521536)

[<u>Read 6 replies</u>](https://x.com/leerob/status/1688639192723521536)

**<u>Place client components in the leaves of the pa</u>g<u>e tree</u>**

To minimize the JavaScript footprint of imported child components, it’s
a good practice to place client components the furthest possible at the
bottom of the components tree.

[<u>Alex
Sidorenko</u>](https://x.com/asidorenko_/status/1693631458982650286)

> 8/18

[<u>@asidorenko\_</u>](https://x.com/asidorenko_/status/1693631458982650286)
[·<u>Follow</u>](https://x.com/intent/follow?screen_name=asidorenko_)

Move client components to the leaves of the component tree where
possible.

> <img src="./jbe4trjh.png"
> style="width:6.86502in;height:3.86662in" />0:00

[<u>Watch on
X</u>](https://x.com/asidorenko_/status/1693631458982650286)

[<u>3:29 PM · Aug 21,
2023</u>](https://x.com/asidorenko_/status/1693631458982650286)

[<u>1.1K</u>](https://x.com/intent/like?tweet_id=1693631458982650286)

[<u>Reply</u>](https://x.com/intent/tweet?in_reply_to=1693631458982650286)

[<u>Read 11
replies</u>](https://x.com/asidorenko_/status/1693631458982650286)

**<u>Be mindful of third-party dependencies’ bundle sizes</u>**

Any client component dependency is more JavaScript for the browser to
download, parse, and execute.

Tools such as [<u>pkg-size</u>](https://pkg-size.dev/) can be used to
determine the size impact of NPM packages based on what’s imported from
them and help decide between alternatives.

> 9/18

<img src="./1at1mpwf.png"
style="width:6.63282in;height:5.9867in" />

**<u>Lazy-load client components when possible</u>**

Even when a client component is necessarily heavy, it’s still possible
to only download its JavaScript once it’s rendered.

For example, [<u>the stockists pa</u>g<u>e on Jennifer
Fisher</u>](https://jenniferfisher.com/pages/stockists) uses mapbox-gl,
an extremely heavy package, to display interactive maps.

Since mapbox-gl is only used to display maps, its wrapper client
component is lazy-loaded so the package bundle is only downloaded when
the component is rendered.

You can lazy-load a client component either via next/dynamic or a
combination of React.lazy and Suspense , more details can be found on
[<u>Next.js guide on the
topic</u>](https://nextjs.org/docs/app/building-your-application/optimizing/lazy-loading).

**<u>Efficiently load third-party scripts</u>**

Some third-party dependencies like Google Tag Manager are injected via
script tags instead of imports in client components.

[<u>@next/third-parties</u>](https://nextjs.org/docs/app/building-your-application/optimizing/third-party-libraries)
can be used to reduce their impact on page render speed and if
dependency is not supported,
[<u>next/script</u>](https://nextjs.org/docs/app/api-reference/components/script)
is also a great option.

> 10/18

**<u>How to load fonts more efficiently</u>**

Some web fonts are unnecessarily heavy because they include characters
not even needed by the website.

In the case of Jennifer Fisher, Tinloof was able to trim out more than
50% of font files using tools such as
[<u>transfonter</u>.](https://transfonter.org/)

[<u>next/font</u>](https://nextjs.org/docs/app/building-your-application/optimizing/fonts)
makes it possible to load local and Google Fonts while providing the
following optimizations:

> 1\. Only load fonts on pages where they are used.
>
> 2\.
> [<u>Preload</u>](https://web.dev/articles/codelab-preload-web-fonts)
> fonts to make them available early on when rendering.
>
> 3\. Use display strategies such as
> [<u>swap</u>](https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display)
> to avoid blocking text rendering by using a fallback font.

**<u>How to load ima</u>g<u>es more efficiently</u>**

**Short** **answer:** use
[<u>next/ima</u>g<u>e</u>](https://nextjs.org/docs/app/building-your-application/optimizing/images)
when you can.

The next/image component provides so many optimizations for local or
remote images.

A detailed guide is available on [<u>Next.</u>j<u>s
docs</u>](https://nextjs.org/docs/app/building-your-application/optimizing/images)
so I’ll only highlight some of them:

> 1\. Images are automatically served in modern efficient formats such
> as AVIF or WebP that preserve quality and dramatically reduce the
> download size.
>
> 2\. Images are only loaded when visible in the viewport and a lazy
> boolean prop is available
>
> [<u>to do the opposite for critical
> images</u>.](https://web.dev/articles/lcp-lazy-loading)
>
> 3\. A preload prop is available to make the browser load critical
> images ASAP.
>
> 4\. Images are automatically served in different sizes based on the
> viewport and props such as sizes or loader are available to customise
> the behaviour.
>
> 5\. Local images can automatically show a placeholder while loading
> and you can provide a
>
> blurDataURL to achieve the same with remote images.

The next/image component is just a very handy utility and is not
required to achieve the benefits above:

> 1\. Images can still be served in modern formats by using CDNs that
> can convert them on the fly.
>
> 2\. Lazy-loading images is a native browser attribute that can be used
> by default.
>
> 11/18
>
> 3\. Images can be preloaded using a preload link
>
> \<link rel="preload" as="image" href="..." /\> in the document’s head
> or using ReactDOM.preload.
>
> 4\. When loading images from a different domain, it’s a good practice
> to use
>
> [<u>preconnect
> links</u>](https://web.dev/articles/preconnect-and-dns-prefetch) to
> inform the browser to establish a connection with the image provider
> domain early-on.

**<u>How to load videos more efficiently</u>**

Solutions such as
[<u>Mux</u>,](https://www.mux.com/blog/multi-cdn-support-in-mux-video-for-improved-performance-and-reliability)
[<u>Cloudinary</u>](https://cloudinary.com/), or CDNs such as
[<u>Fastly</u>](https://www.fastly.com/products/streaming-video-on-demand)
can be used to help optimise video delivery by serving videos as close
as possible to users.

A poster image is a must-have for any video and you can either manually
set it or easily extract the first frame of the video to be the poster
image when using any video CDN.

The best part is that you can use the same image optimizations tips
discussed earlier to render the poster image efficiently.

Here’s an example Mux video component that utilises these optimizations
and it’s only rendered on the server:

> 12/18

import{ preload }from"react-dom";

import{ unstable_getImgProps as getImgProps }from"next/image";

typeProps={

> playbackId:string;
>
> loading:"lazy"\|"eager";
>
> resolution:"SD"\|"HD";

};

exportdefaultfunctionMuxVideo({ playBackId, loading, loading }: Props){

const mp4Url =\`https://stream.mux.com/\${playbackId}/\${

> resolution ==="SD"?"medium":"high"

}.mp4\`;

const webmUrl =\`https://stream.mux.com/\${playbackId}/\${

> resolution ==="SD"?"medium":"high"

}.webm\`;

// Use \`getImgProps\` to convert the video poster image to WebP

const{

> props:{ src: poster },

}=getImgProps({

src:\`https://image.mux.com/\${playbackId}/thumbnail.webp?
fit_mode=smartcrop&time=0\`,

> alt:"",
>
> 13/18
>
> fill:true,

});

// Preload the poster when applicable

if(loading ==="eager"){

preload(poster,{

as:"image",

> fetchPriority:"high",

});

}

return(

\<video

autoPlay

playsInline

loop

controls={false}

muted

preload="none"

\>

\<sourcesrc={mp4Url}type="video/mp4"/\>

\<sourcesrc={webmUrl}type="video/webm"/\>

> 14/18

\</video\>

);

}

For videos that are not required to load immediately, you lazy-load them
without causing any layout shift:

> 15/18

'use client';

importImagefrom'next/image';

import{ useEffect, useState }from'react';

import useInView from'~/hooks/useInView';

importVideo,{VideoProps}from'./Video';

exportdefaultfunctionLazyLoadedVideo(props: VideoProps){

const{ ref, inView }=useInView({ triggerOnce:true});

return(

\<\>

{!inView ?(

\<Image

ref={ref asReact.RefObject\<HTMLImageElement\>}

alt={'Video poster'}

src={props.poster ??''}

className={props.className}

style={props.style}

loading={'lazy'}

layout="fill"

/\>

):(

> 16/18

\<Video{...props}/\><img src="./quav0zq4.png"
style="width:0.48459in;height:0.48459in" />

)}

\</\>

);

}

**<u>How to reduce the HTML document size</u>**

The HTML document is a critical resource the browser has to download and
parse.

**<u>Use virtualization</u>**

Components such as carousels/sliders, tables, and lists are also usual
culprits.

You can use libraries such [<u>TanStack
Virtual</u>](https://tanstack.com/virtual/v3) to only render items when
they are visible in the viewport while avoiding any layout shifts.

**<u>How to speed up user interactions</u>**

**Short** **answer:** Provide feedback to the user as early as possible.

Some user interactions such as URL state navigations when filtering or
adding an item to the cart rely on server responses, which are not
always immediate, causing slow interactions or leaving the user puzzled
on whether something went wrong.

Optimistic UI techniques can be used to make such interactions snappy
and provide immediate feedback to users.

The idea is to use JavaScript to show the predicted result to the user
without waiting the server to return a response.

[It can be achieved either through normal React state management or
using
<u>React’s</u>](https://nextjs.org/docs/app/building-your-application/data-fetching/forms-and-mutations#optimistic-updates)
[<u>useOptimistic
hook</u>.](https://nextjs.org/docs/app/building-your-application/data-fetching/forms-and-mutations#optimistic-updates)

[<u>Alex
Sidorenko</u>](https://x.com/asidorenko_/status/1705586291788730412)

[@<u>asidorenko\_</u>](https://x.com/asidorenko_/status/1705586291788730412)
[·<u>Follow</u>](https://x.com/intent/follow?screen_name=asidorenko_)

Optimistic updates in Next.js 13

> 17/18
>
> <img src="./421h3nic.png"
> style="width:6.86502in;height:3.86662in" />0:00

[<u>Watch on
X</u>](https://x.com/asidorenko_/status/1705586291788730412)

[<u>1.0K</u>](https://x.com/intent/like?tweet_id=1705586291788730412)

[<u>Reply</u>](https://x.com/intent/tweet?in_reply_to=1705586291788730412)

[<u>Read 13
replies</u>](https://x.com/asidorenko_/status/1705586291788730412)

**<u>The importance of a performant website</u>**

Fast websites are more pleasant to use, more engaging to users, and it’s
no surprise they directly impact success metrics such as conversion rate
and search engine indexation.

Although the tips above are focused on Next.js, the concepts behind them
can be used to make any website faster.

> <img src="./a3kuqi0b.png"
> style="width:2.12595in;height:0.16144in" />[**Want** **to** **talk**
> **to** **an** **expert?**](https://vercel.com/contact/sales)

[<u>Brainstorm with our team about your unique use case of
Next.js.</u>](https://vercel.com/contact/sales)

[<u>Send us a messa</u>g<u>e</u>](https://vercel.com/contact/sales)

**Explore** **Vercel** **Enterprise** with an interactive product tour,
trial, or a personalized demo.

[<u>Explore Enterprise</u>](https://vercel.com/try-enterprise)

> 18/18
