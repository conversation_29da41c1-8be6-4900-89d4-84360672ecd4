Mac:kickoffpredict suprimshrestha$ cd frontend && npm run build && npm run dev

> frontend@0.1.0 build
> next build

 ⚠ Warning: Found multiple lockfiles. Selecting /Users/<USER>/package-lock.json.
   Consider removing the lockfiles at:
   * /Users/<USER>/Downloads/kickoffpredict/frontend/package-lock.json
   * /Users/<USER>/Downloads/kickoffpredict/package-lock.json

   ▲ Next.js 15.4.4
   - Environments: .env.local

   Creating an optimized production build ...
 ✓ Compiled successfully in 12.0s

Failed to compile.

./src/app/match/[...slug]/page.tsx
15:16  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
16:12  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
17:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
18:16  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
19:17  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
39:6  Warning: React Hook useEffect has a missing dependency: 'liveFixtures'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
99:37  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
198:6  Warning: React Hook useEffect has a missing dependency: 'fixtureData'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/fixture/FixtureContent.tsx
12:14  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
13:18  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
14:14  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
15:15  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
16:18  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
17:19  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
24:9  Warning: 'isLive' is assigned a value but never used.  @typescript-eslint/no-unused-vars
25:9  Warning: 'isFinished' is assigned a value but never used.  @typescript-eslint/no-unused-vars
87:55  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
90:49  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/components/fixture/FixtureHeader.tsx
5:100  Warning: 'addDays' is defined but never used.  @typescript-eslint/no-unused-vars
92:9  Warning: 'getStatusDisplay' is assigned a value but never used.  @typescript-eslint/no-unused-vars
121:11  Warning: 'minutesUntil' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/fixture/FixtureSidebar.tsx
6:12  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
7:17  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
10:51  Warning: 'predictions' is defined but never used.  @typescript-eslint/no-unused-vars
11:10  Warning: 'standings' is assigned a value but never used.  @typescript-eslint/no-unused-vars
11:46  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
12:10  Warning: 'relatedMatches' is assigned a value but never used.  @typescript-eslint/no-unused-vars
12:56  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
13:10  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
82:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
112:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/fixture/HeadToHead.tsx
4:16  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
5:12  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
19:34  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
37:9  Warning: 'awayTeamId' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/fixture/MatchEvents.tsx
4:12  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
5:12  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
64:17  Warning: 'eventTime' is assigned a value but never used.  @typescript-eslint/no-unused-vars
71:39  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
108:17  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/fixture/MatchLineups.tsx
4:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
5:12  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
30:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
64:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
82:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
106:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
124:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/fixture/MatchStatistics.tsx
4:16  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
5:12  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
186:60  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/fixture/TeamForm.tsx
6:12  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
141:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
149:35  Warning: 'index' is defined but never used.  @typescript-eslint/no-unused-vars
191:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
199:35  Warning: 'index' is defined but never used.  @typescript-eslint/no-unused-vars