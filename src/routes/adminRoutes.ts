import express, { Router, Request, Response, RequestHandler } from 'express';
import { fetchAndUpdateLeagueCoverage } from '../jobs/leagueCoverageJobs';
import { getLeagueCoverageCollection } from '../models/LeagueCoverage';
import { getStandingUpdatesCollection } from '../models/StandingUpdate';
import { fetchAndUpdateStandings } from '../jobs/standingJobs';
import { getStandingsCollection, createStandingId } from '../models/Standing';
import { fetchAndUpdateUpcomingFixtures, fetchAndUpdateUpcomingFixtureLineups } from '../jobs/fixtureJobs';
import { fetchAndUpdateLiveOdds, fetchAndUpdateOdds } from '../jobs/oddsJobs';
import { fetchAndUpdatePredictionsForUpcomingFixtures } from '../jobs/predictionJobs';
import { getFixturesCollection } from '../models/Fixture';
import { closeChat, openChat } from '../models/Message';
import { getRedisClient } from '../config/redis';
import { getSocketIO } from '../server';
import { closeFixtureChat, notifyFixtureChatClosing, notifyFixtureChatOpening } from '../services/chatService';

const router: Router = express.Router();

// Route to trigger the league coverage job
router.post('/jobs/leagueCoverage', async (_req: Request, res: Response) => {
  try {
    console.log('Manually triggering league coverage job...');
    await fetchAndUpdateLeagueCoverage();
    res.status(200).json({ message: 'League coverage job triggered successfully' });
  } catch (error) {
    console.error('Error triggering league coverage job:', error);
    res.status(500).json({ message: 'Failed to trigger league coverage job', error });
  }
});

// Route to trigger the standings job
router.post('/jobs/standings', async (_req: Request, res: Response) => {
  try {
    console.log('Manually triggering standings job...');
    await fetchAndUpdateStandings();
    res.status(200).json({ message: 'Standings job triggered successfully' });
  } catch (error) {
    console.error('Error triggering standings job:', error);
    res.status(500).json({ message: 'Failed to trigger standings job', error });
  }
});

// Route to check if standings exist for a specific league and season
router.get('/standings/check', async (req: Request, res: Response) => {
  try {
    const { leagueId, season } = req.query;

    if (!leagueId || !season) {
      res.status(400).json({ message: 'Missing required parameters: leagueId and season' });
      return;
    }

    const collection = getStandingsCollection();
    const standingId = createStandingId(Number(leagueId), Number(season));
    const standingDoc = await collection.findOne({ _id: standingId });

    res.status(200).json({
      exists: !!standingDoc,
      standingId,
      lastUpdated: standingDoc?.lastUpdated || null
    });
  } catch (error) {
    console.error('Error checking standings:', error);
    res.status(500).json({ message: 'Failed to check standings', error });
  }
});

// Route to get league coverage data
router.get('/leagueCoverage', async (req: Request, res: Response) => {
  try {
    const { id, season } = req.query;
    const collection = getLeagueCoverageCollection();

    // Build the query based on the provided parameters
    const query: any = {};
    if (id) query._id = parseInt(id as string);
    if (season) query.season = parseInt(season as string);

    // Execute the query
    const leagueCoverage = await collection.find(query).toArray();

    res.status(200).json(leagueCoverage);
  } catch (error) {
    console.error('Error fetching league coverage data:', error);
    res.status(500).json({ message: 'Failed to fetch league coverage data', error });
  }
});

// Route to get standing update history
router.get('/standingUpdates', async (req: Request, res: Response) => {
  try {
    const { leagueId, season } = req.query;
    const collection = getStandingUpdatesCollection();

    // Build the query based on the provided parameters
    const query: any = {};
    if (leagueId) query.leagueId = parseInt(leagueId as string);
    if (season) query.season = parseInt(season as string);

    // Execute the query
    const standingUpdates = await collection.find(query).sort({ lastUpdated: -1 }).toArray();

    res.status(200).json(standingUpdates);
  } catch (error) {
    console.error('Error fetching standing update history:', error);
    res.status(500).json({ message: 'Failed to fetch standing update history', error });
  }
});

// Route to trigger the upcoming fixtures job
router.post('/jobs/upcomingFixtures', async (req: Request, res: Response) => {
  try {
    const days = req.query.days ? parseInt(req.query.days as string) : 7;
    console.log(`Manually triggering upcoming fixtures job for the next ${days} days...`);
    await fetchAndUpdateUpcomingFixtures(days);
    res.status(200).json({ message: `Upcoming fixtures job for the next ${days} days triggered successfully` });
  } catch (error) {
    console.error('Error triggering upcoming fixtures job:', error);
    res.status(500).json({ message: 'Failed to trigger upcoming fixtures job', error });
  }
});

// Route to trigger the upcoming fixture lineups job
router.post('/jobs/upcomingFixtureLineups', async (req: Request, res: Response) => {
  try {
    const hours = req.query.hours ? parseInt(req.query.hours as string) : 3;
    console.log(`Manually triggering upcoming fixture lineups job for the next ${hours} hours...`);
    await fetchAndUpdateUpcomingFixtureLineups(hours);
    res.status(200).json({ message: `Upcoming fixture lineups job for the next ${hours} hours triggered successfully` });
  } catch (error) {
    console.error('Error triggering upcoming fixture lineups job:', error);
    res.status(500).json({ message: 'Failed to trigger upcoming fixture lineups job', error });
  }
});

// Route to clear team form cache
const clearTeamFormCacheHandler: RequestHandler = async (req: Request, res: Response) => {
  try {
    const teamId = parseInt(req.params.teamId);
    const season = req.query.season ? parseInt(req.query.season as string) : new Date().getFullYear();

    if (isNaN(teamId)) {
      res.status(400).json({ message: 'Invalid team ID' });
      return;
    }

    const redisClient = getRedisClient();
    const cacheKey = `form:team:${teamId}:${season}`;

    const result = await redisClient.del(cacheKey);
    console.log(`Cleared team form cache for team ${teamId}, season ${season}`);

    res.status(200).json({
      message: `Team form cache cleared for team ${teamId}`,
      cacheKey,
      cleared: result > 0
    });
  } catch (error) {
    console.error('Error clearing team form cache:', error);
    res.status(500).json({ message: 'Failed to clear team form cache', error });
  }
};

router.delete('/cache/team-form/:teamId', clearTeamFormCacheHandler);

// Route to test chat closing functionality
const testChatClosingHandler = async (req: any, res: any) => {
  try {
    const { fixtureId } = req.body;

    if (!fixtureId) {
      return res.status(400).json({ message: 'fixtureId is required' });
    }

    const fixtureCollection = getFixturesCollection();
    const fixture = await fixtureCollection.findOne({ _id: fixtureId });

    if (!fixture) {
      return res.status(404).json({ message: 'Fixture not found' });
    }

    // Notify users that chat will close in 20 minutes
    const io = getSocketIO();
    if (io) {
      notifyFixtureChatClosing(io, fixtureId);
    }

    // Mark the fixture as notified about chat closing
    const chatClosingTime = new Date(Date.now() + 20 * 1000); // 20 seconds for testing instead of 20 minutes
    await fixtureCollection.updateOne(
      { _id: fixtureId },
      { $set: { chatClosingNotified: true, chatClosingTime } }
    );

    res.status(200).json({
      message: 'Chat closing notification sent',
      chatClosingTime
    });

    // Schedule chat closing after 20 seconds
    setTimeout(async () => {
      try {
        // Close the chat
        await closeChat(fixtureId);

        // Mark the fixture as chat closed
        await fixtureCollection.updateOne(
          { _id: fixtureId },
          { $set: { chatClosed: true } }
        );

        // Notify users that chat is now closed
        if (io) {
          closeFixtureChat(io, fixtureId);
        }

        console.log(`Test: Closed chat for fixture ${fixtureId}`);
      } catch (error) {
        console.error('Error closing chat:', error);
      }
    }, 20 * 1000); // 20 seconds

  } catch (error) {
    console.error('Error testing chat closing:', error);
    res.status(500).json({ message: 'Failed to test chat closing', error });
  }
};

router.post('/test/chat-closing', testChatClosingHandler);

// Route to test chat opening functionality
const testChatOpeningHandler = async (req: any, res: any) => {
  try {
    const { fixtureId } = req.body;

    if (!fixtureId) {
      return res.status(400).json({ message: 'fixtureId is required' });
    }

    const fixtureCollection = getFixturesCollection();
    const fixture = await fixtureCollection.findOne({ _id: fixtureId as any });

    if (!fixture) {
      return res.status(404).json({ message: 'Fixture not found' });
    }

    // Open the chat
    await openChat(fixtureId);

    // Mark the fixture as notified about chat opening
    await fixtureCollection.updateOne(
      { _id: fixtureId },
      { $set: { chatOpeningNotified: true } }
    );

    // Notify users that chat is now open
    const io = getSocketIO();
    if (io) {
      notifyFixtureChatOpening(io, fixtureId);
    }

    res.status(200).json({
      message: 'Chat opening notification sent'
    });

    console.log(`Test: Opened chat for fixture ${fixtureId}`);
  } catch (error) {
    console.error('Error testing chat opening:', error);
    res.status(500).json({ message: 'Failed to test chat opening', error });
  }
};

router.post('/test/chat-opening', testChatOpeningHandler);

// Route to trigger the live odds job
router.post('/jobs/liveOdds', async (_req: Request, res: Response) => {
  try {
    console.log('Manually triggering live odds job...');
    await fetchAndUpdateLiveOdds();
    res.status(200).json({ message: 'Live odds job triggered successfully' });
  } catch (error) {
    console.error('Error triggering live odds job:', error);
    res.status(500).json({ message: 'Failed to trigger live odds job', error });
  }
});

// Route to trigger the pre-match odds job
router.post('/jobs/odds', async (_req: Request, res: Response) => {
  try {
    console.log('Manually triggering pre-match odds job...');
    await fetchAndUpdateOdds();
    res.status(200).json({ message: 'Pre-match odds job triggered successfully' });
  } catch (error) {
    console.error('Error triggering pre-match odds job:', error);
    res.status(500).json({ message: 'Failed to trigger pre-match odds job', error });
  }
});

// Route to trigger the predictions job
router.post('/jobs/predictions', async (req: Request, res: Response) => {
  try {
    const days = req.query.days ? parseInt(req.query.days as string) : 2;
    console.log(`Manually triggering predictions job for the next ${days} days...`);
    await fetchAndUpdatePredictionsForUpcomingFixtures(days);
    res.status(200).json({ message: `Predictions job for the next ${days} days triggered successfully` });
  } catch (error) {
    console.error('Error triggering predictions job:', error);
    res.status(500).json({ message: 'Failed to trigger predictions job', error });
  }
});

export default router;
